package com.dep.biguo.common;

import com.dep.biguo.mvp.model.api.Api;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/26
 * @Description: 常量
 */
public interface Constant {

    boolean IS_TEST = false; //测试版

    boolean SINGLE_APP = true; // 单独打包
    String ZK = "ZK";
    String CK = "CK";
    String JSZ = "JSZ";
    String KJ = "KJ";
    String JZS = "JZS";
    String RLZY = "RLZY";
    String YYDJ = "YYDJ";

    String APP_WECHAT_OFFICIAL = "笔果自考题库";
    String APP_PHONE = "0755-89325485";

    String AGREEMENT_VIP = "http://biguotk.com/VIP.html"; //vip协议地址
    String AGREEMENT_SECRET = "http://biguotk.com/ymxy.html"; //押密购买协议
     String AGREEMENT_USER = "https://www.biguotk.com/biguochengkaoAgreement.html"; //用户协议
    String AGREEMENT_USER2 = "https://www.biguotk.com/biguochengkaoPrivacyPolicy.html"; //隐私政策
    String ORGANIZATION_URL = "https://h5.biguotk.com/BiguoServiceSpread/11"; //企业合作
    String ORGANIZATION_LIST_URL = "https://h5.biguotk.com/OrganizationList"; //企业合作
    String JSZ_GUIDE_URL = "https://h5.biguotk.com/app/appTeacherGuide"; //教师职格证考证指南
    String APP_HELP_CENTER = "https://h5.biguotk.com/app/help"; //帮助中心
    String EVERY_DAY_DRAW = "https://h5.biguotk.com/everyDayDraw"; //帮助中心
    String YAMI_REFUND = "https://h5.biguotk.com/yamiRefund"; //押密退款
    String SVIP_PAY_URL = Api.BASE_URL + "v2/h5_super_vip"; //超级VIP H5支付链接
    String EXTEND_RULE = "https://www.biguotk.com/agreement/ExtendRule.html"; //推广活动规则
    String GET_CASH_RULE = "https://www.biguotk.com/agreement/GetCashRule.html"; //提现规则

    String WX_KEY = "wx67b8056fdbba9f70";
    String WX_SECRET = "da716ab63dcabefea294ca14a81766cd";
    String QQ_KEY = "1106164893";
    String QQ_SECRET = "ufvqXmaoX0zTnl4y";

    int ANSWER_NONE = 0; //未作答
    int ANSWER_CORRECT = 1; //选项正确
    int ANSWER_ERROR = 2; //选项错误
    int ANSWER_FAIL = 3; //题目出错

}
