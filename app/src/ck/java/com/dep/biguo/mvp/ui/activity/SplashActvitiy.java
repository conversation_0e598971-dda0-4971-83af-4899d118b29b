package com.dep.biguo.mvp.ui.activity;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.dep.biguo.R;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserHelper;
import com.jess.arms.utils.ArmsUtils;

public class SplashActvitiy extends AppCompatActivity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.AppTheme);
        super.onCreate(savedInstanceState);
        if (!KVHelper.getBoolean(UserHelper.GUIDE)) {
            ArmsUtils.startActivity(GuideActivity.class);
        } else {
            ArmsUtils.startActivity(CKMainActivity.class);
        }
        finish();
    }
}
