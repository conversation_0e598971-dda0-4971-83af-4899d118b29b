package com.dep.biguo.common;

import com.dep.biguo.mvp.model.api.Api;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/26
 * @Description: 常量
 */
public abstract class Constant {
    public static final boolean IS_TEST = true; //测试版

    public static final boolean SINGLE_APP = false; // 单独打包

    public static final String ZK = "0";
    public static final String CK = "1";
    public static final String JSZ = "2";
    public static final String KJ = "KJ";
    public static final String JZS = "JZS";
    public static final String RLZY = "RLZY";
    public static final String YYDJ = "YYDJ";

    public static final String APP_WECHAT_OFFICIAL = "笔果自考题库";
    public static final String APP_PHONE = "0755-89325485";

    public static final String BASE_RELEASE_H5 = "https://h5.biguotk.com";
    public static final String BASE_TEST_H5 = "https://h5dev.biguotk.com";
    public static String BASE_H5 = Api.isReleaseService() ? BASE_RELEASE_H5 : BASE_TEST_H5;//旧H5页面使用

    public static final String BASE_BIGUO_RELEASE_H5 = "https://file.biguotk.com";
    public static String BASE_BIGUO_H5 = BASE_BIGUO_RELEASE_H5;//新H5页面域名，目前只在部分H5页面使用

    public static final String AGREEMENT_VIP = "http://biguotk.com/VIP.html"; //vip协议地址
    public static final String ORGANIZATION_URL = BASE_H5 +"/BiguoServiceSpread/11"; //企业合作
    public static final String ORGANIZATION_LIST_URL = BASE_H5 +"/OrganizationList"; //企业合作
    public static final String JSZ_GUIDE_URL = BASE_H5 +"/app/appTeacherGuide"; //教师职格证考证指南
    public static final String APP_HELP_CENTER = BASE_H5 +"/app/help"; //帮助中心
    public static final String EVERY_DAY_DRAW = BASE_H5 +"/everyDayDraw"; //积分抽奖
    public static final String YAMI_REFUND = BASE_H5 +"/yamiRefund"; //押密退款
    public static final String YAMI_CK_REFUND = BASE_H5 +"/yamiCkRefund"; //成考押密退款
    public static final String DANG_AN_GUAN_LI = BASE_H5 +"/eduTrusteeship"; //档案管理
    public static final String STUDY_PLAN = BASE_H5 +"/learnPlan"; //学梦计划
    public static final String FREE_DAY_LUCK_DRAW = BASE_H5 +"/everyDayDraws"; //每日抽奖
    public static final String INTEGRAL_DAY_LUCK_DRAW = BASE_H5 +"/integralDraw"; //积分抽奖
    public static final String SHARE_APP= BASE_H5 +"/download"; //分享APP
    public static final String SHARE_GROUP = BASE_H5 +"/appGroup"; //分享拼团
    public static final String SHARE_GOODS = BASE_H5 +"/appQuestions"; //分享商品
    public static final String SHARE_INVITATION = BASE_H5 +"/register";//分销邀请
    public static final String FACE_INVITATION = BASE_H5 +"/shareCode";//面对面邀请
    public static final String SHARE_QUESTION = BASE_H5 +"/topicShare";//分享题目
    public static final String SCHOOL_IN_PROFESSION = BASE_H5 +"/famousSchool";//学校的专业介绍
    public static final String SCHOOL_SIGN_UP = BASE_H5 +"/signupWebview";//学校报名
    public static final String ORGANIZATION_REPORT = BASE_H5 +"/applicationSchool";//机构报名
    public static final String SHARE_REAL_PAPER = BASE_H5 +"/historicalQuestions";//分享历年真题
    public static final String INVITE_TO_INTRODUCE = BASE_H5 +"/shareInvitation";//线下邀请转介绍
    public static final String CIRCLE_DETAIL = BASE_H5 + "/dynamicDetails";//圈子详情
    public static final String PEOPLE_INFO_COLL_LIST = BASE_H5 + "/infoCollectionChecklist";//个人信息收集清单
    public static final String WX_GONG_ZHONG_HAO = BASE_H5 +"/followPublicAccount";//微信公众号
    //后台支持修改的协议
    public static final String MX_AGREEMENT_SECRET = BASE_BIGUO_H5 +"/agreement/mxfwxy.html"; //直播密训班购买协议
    public static final String AGREEMENT_SECRET = BASE_BIGUO_H5 +"/agreement/ymxy.html"; //押密购买协议
    public static final String AGREEMENT_CK_SECRET = BASE_BIGUO_H5 +"/agreement/adultAgreement.html"; //成考押密购买协议
    public static final String SCHOOL_RULE = BASE_BIGUO_H5 +"/agreement/schoolRules.html";//校企校规
    public static final String XIXUAN_RULE = BASE_BIGUO_H5 +"/agreement/volunteerClass.html";//自选辅导班的协议
    public static final String AGREEMENT_USER = BASE_BIGUO_H5 +"/agreement/Agreement.html"; //用户协议
    public static final String AGREEMENT_USER4 = BASE_BIGUO_H5 +"/agreement/privacy.html"; //隐私政策
    public static final String SDK_SHARE_LIST = BASE_BIGUO_H5 +"/agreement/sharedChecklist.html"; //第三方共享清单
    public static final String AGREEMENT_USER3 = BASE_BIGUO_H5 +"/agreement/vipAgreement.html"; //笔果折扣卡服务协议
    public static final String EXTEND_RULE = BASE_BIGUO_H5 +"/agreement/ExtendRule.html"; //推广活动规则
    public static final String INTEGRAL_RULE = BASE_BIGUO_H5 +"/agreement/integralRule.html"; //积分规则
    public static final String GET_CASH_RULE = BASE_BIGUO_H5 +"/agreement/GetCashRule.html"; //提现规则
    public static final String ORGANIZATION_RULE = BASE_BIGUO_H5 +"/agreement/studyClassServiceAgreement.html";//机构助学班服务协议

    public static final String SVIP_PAY_URL = Api.isReleaseService() + "v2/h5_super_vip"; //超级VIP H5支付链接

    public static final String WX_KEY = "wx67b8056fdbba9f70";
    public static final String WX_LUNCH_MINI_ID = "gh_9714ba52946a";
    public static final String WX_SECRET = "da716ab63dcabefea294ca14a81766cd";
    public static final String QQ_KEY = "1106164893";
    public static final String QQ_SECRET = "ufvqXmaoX0zTnl4y";

    public static final int ANSWER_NONE = 0; //未作答
    public static final int ANSWER_CORRECT = 1; //选项正确
    public static final int ANSWER_ERROR = 2; //选项错误
    public static final int ANSWER_WRITE_SHOW = 3; //已作答，可以显示答案
    public static final int ANSWER_WRITE_HIDE = 4; //已作答，不可以显示答案
    public static final int ANSWER_FAIL = 5; //题目出错
}
