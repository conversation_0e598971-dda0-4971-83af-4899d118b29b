/*
package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.R;
import com.dep.biguo.app.AppLifecyclesImpl;
import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.ApiCallBack;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.imp.UMLinkImp;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.PrivateDialog;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.qq.e.ads.splash.SplashAD;
import com.qq.e.ads.splash.SplashADListener;
import com.qq.e.comm.util.AdError;
import com.umeng.umlink.MobclickLink;

import java.util.HashMap;
import java.util.Map;

public class SplashActivity extends AppCompatActivity {
    private RelativeLayout rootView;
    private LinearLayout adView;

    private SplashAD splashAD;
    private boolean canJumpToMainActivity;//是否需要跳转到首页
    private boolean jumpToAdvertisement;//是否展示优量汇广告（用于当前页面处于后台运行时记录押密广告是否展示过）
    private CountDownTimer timer = new CountDownTimer(5000, 500) {
        @Override
        public void onTick(long millisUntilFinished) {
            LogUtil.d("dddd", millisUntilFinished);
            //每隔一秒，检测是否获取到广告，若获取到则展示广告并主动取消轮询
            if(splashAD != null && millisUntilFinished < 3500) {
                splashAD.showFullScreenAd(adView);
                jumpToAdvertisement = true;
            }
        }

        @Override
        public void onFinish() {
            //若轮询自动结束都没检测到获取到广告，则跳转首页
            startMainActivity();
        }
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        */
/*if (UserCache.isDayNight()) {
            setTheme(R.style.AppTheme_SplashStyle_Night);
        } else {
            setTheme(R.style.AppTheme_SplashStyle);
        }*//*

        super.onCreate(savedInstanceState);
        StatusBarHelper.setStatusBarColor(this, getResources().getColor(R.color.tran));
        setContentView(R.layout.splash_activity);

        rootView = findViewById(R.id.rootView);
        adView = findViewById(R.id.adView);

        //1.未同意用户协议 -> 弹出协议 -> 弹出引导
        if (!KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)) {
            PrivateDialog dialog = new PrivateDialog(this);
            dialog.setOnAgreementListener(() -> {
                //同意用户协议和隐私政策才开始
                KVHelper.putValue(UserCache.MAIN_AGREEMENT, true);
                KVHelper.putValue(UserCache.MAIN_AGREEMENT_VERSION, "0");

                UserCache.cacheInstallTime();
                AppLifecyclesImpl.init(getApplication());

                //友盟超链
                MobclickLink.getInstallParams(this, new UMLinkImp() {
                    @Override
                    public void onLink(String s, HashMap<String, String> hashMap) {
                        LogUtil.d("dddd", "deeplink open:"+s);
                        LogUtil.d("dddd", hashMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        UserCache.cacheLinkParams(GsonUtils.toJson(hashMap));
                    }

                    @Override
                    public void onInstall(HashMap<String, String> hashMap, Uri uri) {
                        LogUtil.d("dddd", uri.toString());
                        LogUtil.d("dddd", hashMap);

                        Map<String, String> paramsMap = new HashMap<>();
                        for(String key : uri.getQueryParameterNames()){
                            paramsMap.put(key, uri.getQueryParameter(key));
                        }
                        LogUtil.d("dddd", paramsMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        UserCache.cacheLinkParams(GsonUtils.toJson(paramsMap));
                        UserCache.cacheLinkParams(GsonUtils.toJson(hashMap));
                    }
                });

                if(UserCache.getCity() == null){
                    getAdInfo();
                    delayStartActivity();
                }
            });
            dialog.show();
        }else {
            if((UserCache.getUserCache() != null && UserCache.isMemberShip())) {
                new Handler().postDelayed(() -> startMainActivity(), 1000);
            }else {
                delayStartActivity();
                timer.start();
            }
        }
    }

    public void getAdInfo(){
        DeviceCache.cacheUserAgent(ApiCallBack.getUserAgent(SplashActivity.this));
        ApiCallBack.getOAID(SplashActivity.this, s -> {
            DeviceCache.cacheOAID(s);
            LogUtil.d("dddd", s);
        });
        DeviceCache.cacheIMEI(ApiCallBack.getIMEI(SplashActivity.this));
    }

    public void delayStartActivity(){
        if (Constant.JSZ.equals(UserCache.getAppType())) {
            //如果省份专业未缓存直接跳转到省份界面绑定专业
            ArmsUtils.startActivity(JSZMainActivity.class);

        }else if(UserCache.getCity() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ArmsUtils.startActivity(CityActivity.class);
            finish();
        }else if(UserCache.getSchool() == null || UserCache.getProfession() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ArmsUtils.startActivity(InformationEnterActivity.class);
            finish();
        }else {
            showYoulianghuiAdvertisement();
            timer.start();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(KeyEvent.KEYCODE_BACK == keyCode){
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        //从登录页面返回的时候，会经过这里
        */
/*if(UserCache.getCity() == null && KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)){
            delayStartActivity();
            return;
        }*//*


        if(jumpToAdvertisement && !canJumpToMainActivity){
            jumpToAdvertisement = false;
            delayStartActivity();
            canJumpToMainActivity = true;
            return;
        }
        if (canJumpToMainActivity) {
            startMainActivity();
        }
        canJumpToMainActivity = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJumpToMainActivity = false;
    }


    public void startMainActivity(){
        if (canJumpToMainActivity) {
            if (Constant.CK.equals(UserCache.getAppType())) {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    //如果省份专业未缓存直接跳转到省份界面绑定专业
                    ArmsUtils.startActivity(CityActivity.class);

                }else {
                    ArmsUtils.startActivity(CKMainActivity.class);
                }

            } else if (Constant.JSZ.equals(UserCache.getAppType())) {
                //如果省份专业未缓存直接跳转到省份界面绑定专业
                ArmsUtils.startActivity(JSZMainActivity.class);

            } else {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    ArmsUtils.startActivity(CityActivity.class);
                }else {
                    Intent intent = new Intent(this, MainActivity.class);
                    ArmsUtils.startActivity(intent);
                }
            }
            finish();
        } else {
            canJumpToMainActivity = true;
        }
    }

    private void showYoulianghuiAdvertisement(){
        //广告请求超时时长，建议5秒以上,该参数单位为ms
        SplashADListener splashADListener = new SplashADListener() {
            @Override
            public void onADDismissed() {
                LogUtil.d("dddd","广告结束");
                LogUtil.d("dddd", AppManager.getAppManager().getActivityList().size());
                if(AppManager.getAppManager().getActivityList().size() > 0) {
                    startMainActivity();
                }
            }

            @Override
            public void onNoAD(AdError adError) {
                LogUtil.e("dddd", adError.getErrorCode()+"  "+adError.getErrorMsg());
                if(AppManager.getAppManager().getActivityList().size() > 0) {
                    startMainActivity();
                }
            }

            @Override
            public void onADPresent() {
                LogUtil.d("dddd", "onADPresent");
                //广告开始倒计时了，表示广告已展示给用户看到了，此时取消倒计时，如此设计是防止广告不回调。辣鸡优量汇
                timer.cancel();

            }

            @Override
            public void onADClicked() {
                LogUtil.d("dddd", "广告被点击");

            }

            @Override
            public void onADTick(long l) {
                LogUtil.d("dddd", "ADTick");
                //广告开始倒计时了，表示广告已展示给用户看到了，此时取消倒计时，
                //如此设计是防止广告不回调，因为不确定onADPresent()是否会回调，因此为了保证取消能被执行，在此处再添加一次取消。
                //辣鸡优量汇
                timer.cancel();
            }

            @Override
            public void onADExposure() {
                LogUtil.d("dddd", "本条广告奖励（单位分）："+splashAD.getECPM());

            }

            @Override
            public void onADLoaded(long l) {
                LogUtil.d("dddd", "广告加载完成");
            }
        };

        LogUtil.d("dddd", "拉取广告");
        splashAD = new SplashAD(this, "7003587154589724", splashADListener, 0);
        splashAD.fetchFullScreenAdOnly();

        //当广告控件加载完成，会加入到adView控件里，只要监听到控件加入，就调整“跳过”按钮的位置，防止被状态栏遮挡
        adView.setOnHierarchyChangeListener(new ViewGroup.OnHierarchyChangeListener() {
            @Override
            public void onChildViewAdded(View parent, View child) {
                showViewName(rootView, 0);
            }

            @Override
            public void onChildViewRemoved(View parent, View child) {

            }
        });
    }


    */
/**递归找出跳过按钮所在的父布局，当按钮所在的父布局左上角纵坐标小于状态栏的高度时，
     * 设置移动到（状态栏高度+10dp）这个位置，使得用户可以点击到按钮
     * @param view
     * @param layer
     *//*

    private void showViewName(View view, int layer){
        //LogUtil.d("ddddd", getTab(layer)+view.getClass().getName());
        if((view instanceof TextView) && (((TextView)view).getText().toString()).contains("跳过")){
            int[] location = new int[2];
            view.getLocationInWindow(location);
            int statusBarHeight = DisplayHelper.getStatusBarHeight(SplashActivity.this);
            if(location[1] < statusBarHeight){
                ((View)view.getParent()).setY(statusBarHeight);
            }
        }
        if(view instanceof ViewGroup){
            for(int i=0;i<((ViewGroup)view).getChildCount();i++){
                showViewName(((ViewGroup)view).getChildAt(i), layer+1);
            }
        }
    }

    private String getTab(int layer){
        StringBuilder tab = new StringBuilder();
        for(int i=0;i<layer;i++){
            tab.append("\t");
        }
        return tab.toString();
    }
}
*/
