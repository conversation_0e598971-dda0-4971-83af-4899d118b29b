package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.view.KeyEvent;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.biguo.utils.util.GsonUtils;
import com.dep.biguo.R;
import com.dep.biguo.app.AppLifecyclesImpl;
import com.dep.biguo.common.Constant;
import com.dep.biguo.utils.ApiCallBack;
import com.biguo.utils.util.LogUtil;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.imp.UMLinkImp;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.PrivateDialog;
import com.jess.arms.utils.ArmsUtils;
import com.umeng.umlink.MobclickLink;

import java.util.HashMap;
import java.util.Map;

public class SplashThreeActivity extends AppCompatActivity {
    private RelativeLayout rootView;
    private LinearLayout adView;

    private boolean canJumpToMainActivity;//是否需要跳转到首页
    private boolean jumpToAdvertisement;//是否展示优量汇广告（用于当前页面处于后台运行时记录押密广告是否展示过）
    private CountDownTimer timer = new CountDownTimer(500, 500) {
        @Override
        public void onTick(long millisUntilFinished) {
            LogUtil.d("dddd", millisUntilFinished);
        }

        @Override
        public void onFinish() {
            //若轮询自动结束都没检测到获取到广告，则跳转首页
            startMainActivity();
        }
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
if (UserCache.isDayNight()) {
            setTheme(R.style.AppTheme_SplashStyle_Night);
        } else {
            setTheme(R.style.AppTheme_SplashStyle);
        }

        super.onCreate(savedInstanceState);
        StatusBarHelper.setStatusBarColor(this, getResources().getColor(R.color.tran));
        setContentView(R.layout.splash_activity);

        rootView = findViewById(R.id.rootView);
        adView = findViewById(R.id.adView);

        //1.未同意用户协议 -> 弹出协议 -> 弹出引导
        if (!KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)) {
            PrivateDialog dialog = new PrivateDialog(this);
            dialog.setOnAgreementListener(() -> {
                //同意用户协议和隐私政策才开始
                KVHelper.putValue(UserCache.MAIN_AGREEMENT, true);
                KVHelper.putValue(UserCache.MAIN_AGREEMENT_VERSION, "0");

                UserCache.cacheInstallTime();
                AppLifecyclesImpl.init(getApplication());

                //友盟超链
                MobclickLink.getInstallParams(this, new UMLinkImp() {
                    @Override
                    public void onLink(String s, HashMap<String, String> hashMap) {
                        LogUtil.d("dddd", "deeplink open:"+s);
                        LogUtil.d("dddd", hashMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        UserCache.cacheLinkParams(GsonUtils.toJson(hashMap));
                    }

                    @Override
                    public void onInstall(HashMap<String, String> hashMap, Uri uri) {
                        LogUtil.d("dddd", uri.toString());
                        LogUtil.d("dddd", hashMap);

                        Map<String, String> paramsMap = new HashMap<>();
                        for(String key : uri.getQueryParameterNames()){
                            paramsMap.put(key, uri.getQueryParameter(key));
                        }
                        LogUtil.d("dddd", paramsMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        UserCache.cacheLinkParams(GsonUtils.toJson(paramsMap));
                        UserCache.cacheLinkParams(GsonUtils.toJson(hashMap));
                    }
                });

                if(UserCache.getCity() == null){
                    getAdInfo();
                    delayStartActivity();
                }
            });
            dialog.show();
        }else {
            if((UserCache.getUserCache() != null && UserCache.isMemberShip())) {
                new Handler().postDelayed(() -> startMainActivity(), 1000);
            }else {
                delayStartActivity();
                timer.start();
            }
        }
    }

    public void getAdInfo(){
        DeviceCache.cacheUserAgent(ApiCallBack.getUserAgent(SplashThreeActivity.this));
        ApiCallBack.getOAID(SplashThreeActivity.this, s -> {
            DeviceCache.cacheOAID(s);
            LogUtil.d("dddd", s);
        });
        DeviceCache.cacheIMEI(ApiCallBack.getIMEI(SplashThreeActivity.this));
    }

    public void delayStartActivity(){
        if (Constant.JSZ.equals(UserCache.getAppType())) {
            //如果省份专业未缓存直接跳转到省份界面绑定专业
            ArmsUtils.startActivity(JSZMainActivity.class);

        }else if(UserCache.getCity() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ArmsUtils.startActivity(CityActivity.class);
            finish();
        }else if(UserCache.getSchool() == null || UserCache.getProfession() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ProfessionSchoolActivity.Start(this);
            finish();
        }else {
            timer.start();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(KeyEvent.KEYCODE_BACK == keyCode){
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        //从登录页面返回的时候，会经过这里
if(UserCache.getCity() == null && KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)){
            delayStartActivity();
            return;
        }


        if(jumpToAdvertisement && !canJumpToMainActivity){
            jumpToAdvertisement = false;
            delayStartActivity();
            canJumpToMainActivity = true;
            return;
        }
        if (canJumpToMainActivity) {
            startMainActivity();
        }
        canJumpToMainActivity = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJumpToMainActivity = false;
    }


    public void startMainActivity(){
        if (canJumpToMainActivity) {
            if (Constant.CK.equals(UserCache.getAppType())) {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    //如果省份专业未缓存直接跳转到省份界面绑定专业
                    ArmsUtils.startActivity(CityActivity.class);

                }else {
                    ArmsUtils.startActivity(CKMainActivity.class);
                }

            } else if (Constant.JSZ.equals(UserCache.getAppType())) {
                //如果省份专业未缓存直接跳转到省份界面绑定专业
                ArmsUtils.startActivity(JSZMainActivity.class);

            } else {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    ArmsUtils.startActivity(CityActivity.class);
                }else {
                    Intent intent = new Intent(this, MainActivity.class);
                    ArmsUtils.startActivity(intent);
                }
            }
            finish();
        } else {
            canJumpToMainActivity = true;
        }
    }
}
