package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.biguo.utils.util.AppUtil;
import com.biguo.utils.util.DisplayHelper;
import com.biguo.utils.util.GsonUtils;
import com.biguo.utils.util.LogUtil;
import com.bytedance.sdk.openadsdk.AdSlot;
import com.bytedance.sdk.openadsdk.CSJAdError;
import com.bytedance.sdk.openadsdk.CSJSplashAd;
import com.bytedance.sdk.openadsdk.TTAdLoadType;
import com.bytedance.sdk.openadsdk.TTAdNative;
import com.bytedance.sdk.openadsdk.TTAdSdk;
import com.dep.biguo.R;
import com.dep.biguo.app.AppLifecyclesImpl;
import com.dep.biguo.common.Constant;
import com.dep.biguo.dialog.PrivateDialog;
import com.dep.biguo.utils.ApiCallBack;
import com.dep.biguo.utils.StatusBarHelper;
import com.dep.biguo.utils.imp.UMLinkImp;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.dep.biguo.utils.mmkv.KVHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.jess.arms.integration.AppManager;
import com.jess.arms.utils.ArmsUtils;
import com.umeng.umlink.MobclickLink;

import java.util.HashMap;
import java.util.Map;

public class SplashTowActivity extends AppCompatActivity {
    private RelativeLayout rootView;
    private LinearLayout adView;

    private boolean canJumpToMainActivity;//是否需要跳转到首页
    private boolean jumpToAdvertisement;//是否展示优量汇广告（用于当前页面处于后台运行时记录押密广告是否展示过）

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarHelper.setStatusBarColor(this, getResources().getColor(R.color.tran));
        setContentView(R.layout.splash_activity);

        rootView = findViewById(R.id.rootView);
        adView = findViewById(R.id.adView);

        adView.setFitsSystemWindows(true);

        LogUtil.d("dddd", getClass().getName());
        //1.未同意用户协议 -> 弹出协议 -> 弹出引导
        if (!KVHelper.getBoolean(UserCache.MAIN_AGREEMENT)) {
            PrivateDialog dialog = new PrivateDialog(this);
            dialog.setOnAgreementListener(() -> {
                //同意用户协议和隐私政策才开始
                KVHelper.putValue(UserCache.MAIN_AGREEMENT, true);
                KVHelper.putValue(UserCache.MAIN_AGREEMENT_VERSION, "0");

                UserCache.cacheInstallTime();
                AppLifecyclesImpl.init(getApplication());

                //友盟超链
                MobclickLink.getInstallParams(this, new UMLinkImp() {
                    @Override
                    public void onLink(String s, HashMap<String, String> hashMap) {
                        LogUtil.d("dddd", "deeplink open:"+s);
                        LogUtil.d("dddd", hashMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        LogUtil.d("dddd", getClass().getName());
                        UserCache.cacheLinkParams(GsonUtils.toJson(hashMap));
                    }

                    @Override
                    public void onInstall(HashMap<String, String> hashMap, Uri uri) {
                        LogUtil.d("dddd", uri.toString());
                        LogUtil.d("dddd", hashMap);

                        Map<String, String> paramsMap = new HashMap<>();
                        for(String key : uri.getQueryParameterNames()){
                            paramsMap.put(key, uri.getQueryParameter(key));
                        }
                        LogUtil.d("dddd", paramsMap);
                        //为获取新装参数的处理，App首次安装启动时，开发者调取getInstallParams新安装参数接口会走这个回调
                        LogUtil.d("dddd", getClass().getName());
                        UserCache.cacheLinkParams(GsonUtils.toJson(paramsMap));
                    }
                });

                if(UserCache.getCity() == null){
                    getAdInfo();
                    delayStartActivity();
                }
            });
            dialog.show();
        }else {
            if((UserCache.getUserCache() != null && UserCache.isMemberShip())) {
                new Handler().postDelayed(() -> startMainActivity(), 1000);
            }else {
                delayStartActivity();
            }
        }
    }

    public void getAdInfo(){
        DeviceCache.cacheUserAgent(ApiCallBack.getUserAgent(SplashTowActivity.this));
        ApiCallBack.getOAID(SplashTowActivity.this, s -> {
            DeviceCache.cacheOAID(s);
            LogUtil.d("dddd", s);
        });
        DeviceCache.cacheIMEI(ApiCallBack.getIMEI(SplashTowActivity.this));
    }

    public void delayStartActivity(){
        if (Constant.JSZ.equals(UserCache.getAppType())) {
            //如果省份专业未缓存直接跳转到省份界面绑定专业
            ArmsUtils.startActivity(JSZMainActivity.class);

        }else if(UserCache.getCity() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ArmsUtils.startActivity(CityActivity.class);
            finish();
        }else if(UserCache.getSchool() == null || UserCache.getProfession() == null){
            //如果城市学校专业未缓存，直接跳转到城市界面
            ArmsUtils.startActivity(InformationEnterActivity.class);
            finish();
        }else {
            showChuanShanJiaAdvertisement();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(KeyEvent.KEYCODE_BACK == keyCode){
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(jumpToAdvertisement && !canJumpToMainActivity){
            jumpToAdvertisement = false;
            delayStartActivity();
            canJumpToMainActivity = true;
            return;
        }
        if (canJumpToMainActivity) {
            startMainActivity();
        }
        canJumpToMainActivity = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        canJumpToMainActivity = false;
    }


    public void startMainActivity(){
        if (canJumpToMainActivity) {
            if (Constant.CK.equals(UserCache.getAppType())) {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    //如果省份专业未缓存直接跳转到省份界面绑定专业
                    ArmsUtils.startActivity(CityActivity.class);

                }else {
                    ArmsUtils.startActivity(CKMainActivity.class);
                }

            } else if (Constant.JSZ.equals(UserCache.getAppType())) {
                //如果省份专业未缓存直接跳转到省份界面绑定专业
                ArmsUtils.startActivity(JSZMainActivity.class);

            } else {
                if (UserCache.getCity() == null || UserCache.getProfession() == null) {
                    ArmsUtils.startActivity(CityActivity.class);
                }else {
                    Intent intent = new Intent(this, MainActivity.class);
                    ArmsUtils.startActivity(intent);
                }
            }
            finish();
        } else {
            canJumpToMainActivity = true;
        }
    }

    private void showChuanShanJiaAdvertisement(){
        TTAdNative mTTAdNative = TTAdSdk.getAdManager().createAdNative(this);
        int width = DisplayHelper.getWindowWidth(this);
        int height = DisplayHelper.getWindowHeight(this);
        //创建广告请求AdSlot
        AdSlot adSlot = new AdSlot.Builder()
                .setCodeId("889976614")//广告位ID
                //不区分渲染方式，要求开发者同时设置setImageAcceptedSize（单位：px）和setExpressViewAcceptedSize（单位：dp ）接口，不同时设置可能会导致展示异常。
                .setImageAcceptedSize(width, height)
                .setExpressViewAcceptedSize(DisplayHelper.px2dp(this, width), DisplayHelper.px2dp(this, height))
                .setAdLoadType(TTAdLoadType.PRELOAD)//推荐使用，用于标注此次的广告请求用途为预加载（当做缓存）还是实时加载，方便后续为开发者优化相关策略
                .build();

        // 请求广告
        mTTAdNative.loadSplashAd(adSlot, new TTAdNative.CSJSplashAdListener() {
            //开屏素材加载成功
            @Override
            public void onSplashLoadSuccess(CSJSplashAd csjSplashAd) {
                LogUtil.d("dddd", "开屏素材加载成功");

            }

            //加载开屏素材失败
            @Override
            public void onSplashLoadFail(CSJAdError error) {
                LogUtil.d("dddd", "加载开屏素材失败"+"  "+error.getCode()+"  "+error.getMsg());
                //开发者处理跳转到APP主页面逻辑
                if(!AppManager.getAppManager().getActivityList().isEmpty()) {
                    startMainActivity();
                }
            }

            //开屏渲染成功，可以展示开屏
            @Override
            public void onSplashRenderSuccess(CSJSplashAd ad) {
                LogUtil.d("dddd", "开屏渲染成功");
                if(ad == null) return;
                LogUtil.d("dddd", "开屏渲染成功，ad不为空");

                if(!SplashTowActivity.this.isFinishing()){
                    LogUtil.d("dddd", "开屏渲染成功，显示广告");
                    adView.removeAllViews();
                    adView.setBackgroundColor(AppUtil.getColorRes(SplashTowActivity.this, R.color.twhite));
                    ad.showSplashView(adView);
                    ad.setSplashAdListener(new CSJSplashAd.SplashAdListener() {
                        @Override
                        public void onSplashAdShow(CSJSplashAd csjSplashAd) {

                        }

                        @Override
                        public void onSplashAdClick(CSJSplashAd csjSplashAd) {

                        }

                        @Override
                        public void onSplashAdClose(CSJSplashAd csjSplashAd, int i) {
                            //广告倒计时结束
                            if(!AppManager.getAppManager().getActivityList().isEmpty()) {
                                startMainActivity();
                            }
                        }
                    });
                }else {
                    //开发者处理跳转到APP主页面逻辑
                    LogUtil.d("dddd", "开屏渲染成功，当前页面关闭中");
                    if(!AppManager.getAppManager().getActivityList().isEmpty()) {
                        startMainActivity();
                    }
                }
            }

            @Override
            public void onSplashRenderFail(CSJSplashAd ad, CSJAdError csjAdError) {
                LogUtil.d("dddd", "开屏广告失败:"+csjAdError.getCode()+"    "+csjAdError.getMsg());
                //开发者处理跳转到APP主页面逻辑
                if(!AppManager.getAppManager().getActivityList().isEmpty()) {
                    startMainActivity();
                }
            }
        }, 5000);
    }
}
