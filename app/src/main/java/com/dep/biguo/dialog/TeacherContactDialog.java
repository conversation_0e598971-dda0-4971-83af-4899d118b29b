package com.dep.biguo.dialog;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.PopTeacherBean;
import com.dep.biguo.utils.MainAppUtils;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.utils.image.ImageLoader;
import com.tbruyelle.rxpermissions2.RxPermissions;

import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;

/**
 * @Author: created by biguo
 * @CreatedDate :2019年11月19日10:48:39
 * @Description: 答题报告分享弹窗
 */
public class TeacherContactDialog extends DialogFragment {

    private static final String TAG = "TeacherContactDialog";
    private static final String BEAN = "PopTeacherBean";

    private ImageView ivAvatar;
    private ImageView ivWechat;
    private ImageView ivPhone;
    private TextView tvName;
    private TextView tvSchool;
    private TextView tvWechat;
    private PopTeacherBean bean;

    public static TeacherContactDialog newInstance(PopTeacherBean bean) {
        TeacherContactDialog mTeacherContactDialog = new TeacherContactDialog();
        Bundle bundle = new Bundle();
        bundle.putParcelable(BEAN, bean);
        mTeacherContactDialog.setArguments(bundle);
        return mTeacherContactDialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        bean = getArguments().getParcelable(BEAN);
        View view = inflater.inflate(R.layout.dialog_practice_teacher_contact, container, false);

        view.findViewById(R.id.ivWechat).setOnClickListener(v -> {
            dismiss();
            if (!TextUtils.isEmpty(bean.getWx_number()))
                MainAppUtils.copyWechat(getActivity(), getFragmentManager(), bean.getWx_number());
        });
        view.findViewById(R.id.ivPhone).setOnClickListener(v -> {
            if (!TextUtils.isEmpty(bean.getMobile()))
                new RxPermissions(this)
                        .request(Manifest.permission.CALL_PHONE)
                        .subscribe(aBoolean -> {
                            if (aBoolean)
                                AppUtil.callPhone(getActivity(), bean.getMobile());
                        });
        });
        ivAvatar = view.findViewById(R.id.ivAvatar);
        tvName = view.findViewById(R.id.tvName);
        tvSchool = view.findViewById(R.id.tvSchool);
        tvWechat = view.findViewById(R.id.tvWechat);
        setAvatar("");
        setName(bean.getName());
        setWechat(bean.getMobile());
        setSchool(bean.getCampus());
        view.findViewById(R.id.ivClose).setOnClickListener(v -> dismiss());
        return view;
    }

    public void show(FragmentManager manager) {
        if (isAdded())
            manager.beginTransaction().remove(this).commit();
        show(manager, TAG);
    }

    private void setAvatar(String url) {
        ImageLoader.loadAvatar(ivAvatar, url);
    }

    private void setName(String name) {
        if (!TextUtils.isEmpty(name)) {
            tvName.setText(name);
        }
    }

    private void setWechat(String wechat) {
        if (!TextUtils.isEmpty(wechat)) {
            tvWechat.setText(wechat);
        }
    }

    private void setSchool(String school) {
        if (!TextUtils.isEmpty(school)) {
            tvSchool.setText(school);
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        Dialog dialog = getDialog();
        if (dialog != null) {
            dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            dialog.setCanceledOnTouchOutside(false);
        }
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        try {
            Class<?> clazz = Class.forName("androidx.fragment.app.DialogFragment");
            Field dismissed = clazz.getDeclaredField("mDismissed");
            Field shownByMe = clazz.getDeclaredField("mShownByMe");
            dismissed.setAccessible(false);
            shownByMe.setAccessible(true);
            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            // 这里吧原来的commit()方法换成了commitAllowingStateLoss()
            ft.commitAllowingStateLoss();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }

    private static final String SAVED_DIALOG_STATE_TAG = "android:savedDialogState";
    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        requireActivity().getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull @NotNull LifecycleOwner source, @NonNull @NotNull Lifecycle.Event event) {
                if (event.getTargetState() == Lifecycle.State.CREATED){
                    //在这里任你飞翔
                    if (getShowsDialog()) {
                        setShowsDialog(false);
                    }
                    setShowsDialog(true);

                    View view = getView();
                    if (view != null) {
                        getDialog().setContentView(view);
                    }
                    getLifecycle().removeObserver(this);  //这里是删除观察者
                }
            }
        });
    }
}
