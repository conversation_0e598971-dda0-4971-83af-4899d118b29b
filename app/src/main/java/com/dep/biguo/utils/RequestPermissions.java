package com.dep.biguo.utils;

import android.content.Context;
import android.text.TextUtils;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.PermissionChecker;
import androidx.fragment.app.Fragment;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.app.ResponseErrorListenerImpl;
import com.dep.biguo.dialog.NewMessageDialog;
import com.dep.biguo.utils.mmkv.DeviceCache;
import com.jess.arms.utils.PermissionUtil;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.util.List;

import me.jessyan.rxerrorhandler.core.RxErrorHandler;


public class RequestPermissions {
    public static final int PERMISSION_GRANTED = 0;//允许
    public static final int PERMISSION_DENIED = -1;//拒绝
    public static final int PERMISSION_DENIED_PROHIBIT = -2;//禁止
    public static final int PERMISSION_DENIED_NOT = -3;//不允许问
    public static final int PERMISSION_NULL = -4;//首次询问

    private Builder builder;

    private RequestPermissions(Builder builder){
        this.builder = builder;
    }

    public void showDialog(Context context){
        if(!TextUtils.isEmpty(builder.requestContentText)) {
            //若没有获取到权限，则弹出弹窗提醒
            new NewMessageDialog.Builder(context)
                    .setTitle("请求权限")
                    .setContent(builder.requestContentText)
                    .setNegativeText("拒绝")
                    .setOnNegativeClickListener(v -> {
                        LogUtil.d("dddd", "用户拒绝申请权限，并且不准再弹自定义弹窗");
                        DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_DENIED_NOT);
                        builder.onRequestResultListener.onFailure(true);
                    })
                    .setOnCancelListener(() -> {
                        LogUtil.d("dddd", "取消了弹窗，默认为拒绝");
                        DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_DENIED_NOT);
                        builder.onRequestResultListener.onFailure(true);
                    })
                    .setPositiveText("允许")
                    .setOnPositiveClickListener(v -> requestLocationPermission())
                    .build()
                    .show();
        }else {
            requestLocationPermission();
        }
    }

    public void requestLocationPermission() {
        RxPermissions rxPermissions;
        if(builder.activity != null){
            rxPermissions = new RxPermissions(builder.activity);
        }else {
            rxPermissions = new RxPermissions(builder.fragment);
        }
        PermissionUtil.requestPermission(new PermissionUtil.RequestPermission() {
            @Override
            public void onRequestPermissionSuccess() {
                LogUtil.d("dddd", "用户授予权限，可以继续操作");
                DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_GRANTED);
                builder.onRequestResultListener.onSuccess();
            }

            @Override
            public void onRequestPermissionFailure(List<String> permissions) {
                LogUtil.d("dddd", "用户拒绝权限，但是还可以再继续索要");
                DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_DENIED);
                builder.onRequestResultListener.onFailure(true);
            }

            @Override
            public void onRequestPermissionFailureWithAskNeverAgain(List<String> permissions) {
                LogUtil.d("dddd", "用户拒绝权限，并且不准再索要");
                DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_DENIED_PROHIBIT);
                builder.onRequestResultListener.onNotAsk(true);
            }
        }, rxPermissions, builder.mErrorHandler, builder.permissions);

    }

    public void request(){
        LogUtil.d("dddd", PermissionChecker.checkSelfPermission(getContext(), builder.permissions));
        //检查是否已获得权限，主要是用于在系统设置中赋予权限
        if(PermissionChecker.checkSelfPermission(getContext(), builder.permissions) == PermissionChecker.PERMISSION_GRANTED) {
            //已获得
            DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_GRANTED);
            LogUtil.d("dddd", "检测到已经获得了权限");
            builder.onRequestResultListener.onSuccess();

        }else {
            if(DeviceCache.getPermissionStatus(builder.permissions) == PERMISSION_DENIED_NOT){
                //不准再问
                LogUtil.d("dddd", "检测到上次就明确表示拒绝，并且不准再弹自定义弹窗");
                builder.onRequestResultListener.onFailure(false);

            }else if(DeviceCache.getPermissionStatus(builder.permissions) == PERMISSION_DENIED_PROHIBIT){
                //被禁止了
                LogUtil.d("dddd", "检测到上次就拒绝给权限，并且不准再索要");
                builder.onRequestResultListener.onNotAsk(false);

            }else if(DeviceCache.getPermissionStatus(builder.permissions) == PERMISSION_DENIED && builder.isAgainAsk){
                //上次被拒绝，但是主动触发了
                LogUtil.d("dddd", "检测到上次就明确表示拒绝，但是用户又主动触发");
                showDialog(getContext());

            }else if(DeviceCache.getPermissionStatus(builder.permissions) == PERMISSION_DENIED && !builder.isAgainAsk){
                //上次被拒绝，但是主动触发了
                LogUtil.d("dddd", "检测到上次就明确表示拒绝，系统又自动调用，保持原有不变");
                builder.onRequestResultListener.onFailure(false);

            }else if(DeviceCache.getPermissionStatus(builder.permissions) == PERMISSION_NULL){
                //或第一次询问
                showDialog(getContext());

            }else {
                LogUtil.d("dddd", "啥也不是，就当被禁止了吧，该问题可能是用户在系统中设置了禁止");
                DeviceCache.cachePermissionStatus(builder.permissions, PERMISSION_DENIED_PROHIBIT);
                builder.onRequestResultListener.onNotAsk(false);
            }
        }
    }

    public Context getContext(){
        return builder.activity == null ? builder.fragment.getContext() : builder.activity;
    }

    public static class Builder{
        private Fragment fragment;
        private AppCompatActivity activity;
        private RxErrorHandler mErrorHandler;
        private String permissions;
        private String requestContentText;
        private boolean isAgainAsk;
        private OnRequestResultListener onRequestResultListener;

        public Builder(AppCompatActivity activity) {
            this.activity = activity;
            this.mErrorHandler = RxErrorHandler.builder()
                    .with(activity)
                    .responseErrorListener(new ResponseErrorListenerImpl())
                    .build();
        }

        public Builder(Fragment fragment) {
            this.fragment = fragment;
            this.mErrorHandler = RxErrorHandler.builder()
                    .with(fragment.getContext())
                    .responseErrorListener(new ResponseErrorListenerImpl())
                    .build();
        }

        public Builder setPermissions(String permissions, boolean isAgainAsk) {
            this.permissions = permissions;
            this.isAgainAsk = isAgainAsk;
            //拒绝后，需要再次索要的弹窗
            if(isAgainAsk && DeviceCache.getPermissionStatus(permissions) == PERMISSION_DENIED_NOT) {
                DeviceCache.cachePermissionStatus(permissions, PERMISSION_DENIED);
            }
            return this;
        }

        public Builder setRequestContentText(String requestContentText) {
            this.requestContentText = requestContentText;
            return this;
        }

        public Builder setOnRequestResultListener(OnRequestResultListener onRequestResultListener) {
            this.onRequestResultListener = onRequestResultListener;
            return this;
        }

        public void build(){
            RequestPermissions request = new RequestPermissions(this);
            request.request();
        }
    }

    public interface OnRequestResultListener{
        //申请结果，允许
        void onSuccess();
        //申请结果,拒绝，是否显示了申请权限的弹窗(自定义/系统)
        void onFailure(boolean isShowRequestContentDialog);
        //申请结果,禁止
        void onNotAsk(boolean isShowRequestContentDialog);
    }
}
