package com.dep.biguo.bean;

/**
 * Data model for VIP comparison table rows
 */
public class TableRowData {
    
    public enum RowType {
        HEADER,     // 表头行
        TEXT,       // 文本行
        ICON        // 图标行
    }
    
    public enum CellType {
        TEXT,       // 文本内容
        ICON        // 图标内容
    }
    
    private RowType rowType;
    private String privilege;           // 特权列内容
    private CellType superVipType;      // 超级会员列类型
    private String superVipText;        // 超级会员列文本
    private int superVipIcon;           // 超级会员列图标资源ID
    private CellType vipType;           // 会员列类型
    private String vipText;             // 会员列文本
    private int vipIcon;                // 会员列图标资源ID
    private CellType regularType;       // 普通用户列类型
    private String regularText;         // 普通用户列文本
    private int regularIcon;            // 普通用户列图标资源ID
    
    // Constructor for header row
    public TableRowData(String privilege, String superVip, String vip, String regular) {
        this.rowType = RowType.HEADER;
        this.privilege = privilege;
        this.superVipType = CellType.TEXT;
        this.superVipText = superVip;
        this.vipType = CellType.TEXT;
        this.vipText = vip;
        this.regularType = CellType.TEXT;
        this.regularText = regular;
    }
    
    // Constructor for text row
    public static TableRowData createTextRow(String privilege, String superVip, String vip, String regular) {
        TableRowData row = new TableRowData();
        row.rowType = RowType.TEXT;
        row.privilege = privilege;
        row.superVipType = CellType.TEXT;
        row.superVipText = superVip;
        row.vipType = CellType.TEXT;
        row.vipText = vip;
        row.regularType = CellType.TEXT;
        row.regularText = regular;
        return row;
    }
    
    // Constructor for icon row
    public static TableRowData createIconRow(String privilege, int superVipIcon, int vipIcon, int regularIcon) {
        TableRowData row = new TableRowData();
        row.rowType = RowType.ICON;
        row.privilege = privilege;
        row.superVipType = CellType.ICON;
        row.superVipIcon = superVipIcon;
        row.vipType = CellType.ICON;
        row.vipIcon = vipIcon;
        row.regularType = CellType.ICON;
        row.regularIcon = regularIcon;
        return row;
    }
    
    // Mixed constructor for rows with both text and icons
    public static TableRowData createMixedRow(String privilege, 
                                            CellType superVipType, String superVipText, int superVipIcon,
                                            CellType vipType, String vipText, int vipIcon,
                                            CellType regularType, String regularText, int regularIcon) {
        TableRowData row = new TableRowData();
        row.rowType = RowType.TEXT; // Default to text for mixed
        row.privilege = privilege;
        row.superVipType = superVipType;
        row.superVipText = superVipText;
        row.superVipIcon = superVipIcon;
        row.vipType = vipType;
        row.vipText = vipText;
        row.vipIcon = vipIcon;
        row.regularType = regularType;
        row.regularText = regularText;
        row.regularIcon = regularIcon;
        return row;
    }
    
    private TableRowData() {}
    
    // Getters
    public RowType getRowType() { return rowType; }
    public String getPrivilege() { return privilege; }
    public CellType getSuperVipType() { return superVipType; }
    public String getSuperVipText() { return superVipText; }
    public int getSuperVipIcon() { return superVipIcon; }
    public CellType getVipType() { return vipType; }
    public String getVipText() { return vipText; }
    public int getVipIcon() { return vipIcon; }
    public CellType getRegularType() { return regularType; }
    public String getRegularText() { return regularText; }
    public int getRegularIcon() { return regularIcon; }
}
